import os
import time
import requests
import speech_recognition as sr
import google.generativeai as genai

# Configuration: set these environment variables before running
env_token = os.getenv("CLICKUP_TOKEN")
env_team_id = os.getenv("CLICKUP_TEAM_ID")  # ClickUp Team (Workspace) ID
gemini_api_key = os.getenv("GEMINI_API_KEY")

if not env_token or not env_team_id:
    raise EnvironmentError(
        "Please set CLICKUP_TOKEN and CLICKUP_TEAM_ID environment variables."
    )

if not gemini_api_key:
    raise EnvironmentError(
        "Please set GEMINI_API_KEY environment variable."
    )

# Configure Gemini
genai.configure(api_key=gemini_api_key)

CLICKUP_BASE_URL = "https://api.clickup.com/api/v2"
HEADERS = {
    "Authorization": env_token,
    "Content-Type": "application/json"
}


def fetch_lists():
    """
    Fetch all lists in the specified team (workspace).
    Returns a dict mapping list names (lowercased) to their IDs.
    """
    url = f"{CLICKUP_BASE_URL}/team/{env_team_id}/list"
    resp = requests.get(url, headers=HEADERS)
    resp.raise_for_status()
    data = resp.json().get('lists', [])
    return {lst['name'].lower(): lst['id'] for lst in data}


def create_clickup_task(list_id: str, task_name: str, task_desc: str = "") -> dict:
    """
    Create a new task in ClickUp under the given list ID.
    """
    url = f"{CLICKUP_BASE_URL}/list/{list_id}/task"
    payload = {"name": task_name, "description": task_desc}
    resp = requests.post(url, headers=HEADERS, json=payload)
    resp.raise_for_status()
    return resp.json()


def match_list_with_llm(speech_text: str, available_lists: dict) -> tuple[str, float]:
    """
    Use Gemini to intelligently match speech input with available ClickUp lists.

    Args:
        speech_text: The recognized speech text
        available_lists: Dict mapping list names (lowercased) to their IDs

    Returns:
        Tuple of (matched_list_name, confidence_score)
        Returns (None, 0.0) if no good match is found
    """
    try:
        # Create the model
        model = genai.GenerativeModel('gemini-1.5-flash')

        # Prepare the prompt
        list_names = list(available_lists.keys())
        prompt = f"""
You are helping match speech input to ClickUp list names.

Speech input: "{speech_text}"
Available lists: {list_names}

Your task:
1. Find the best matching list name from the available lists
2. Consider variations, synonyms, and partial matches
3. Be flexible with speech recognition errors

Examples:
- "shopping list" could match "shopping" or "groceries"
- "work stuff" could match "work" or "tasks"
- "personal things" could match "personal"

Respond with ONLY the exact list name from the available lists that best matches, or "NONE" if no reasonable match exists.
Do not include any explanation or additional text.
"""

        # Get response from Gemini
        response = model.generate_content(prompt)
        matched_list = response.text.strip().lower()

        # Check if the matched list exists in our available lists
        if matched_list in available_lists:
            return matched_list, 0.9  # High confidence for LLM match
        elif matched_list == "none":
            return None, 0.0
        else:
            # LLM returned something not in our list, try fuzzy matching
            for list_name in available_lists.keys():
                if matched_list in list_name or list_name in matched_list:
                    return list_name, 0.7  # Medium confidence for fuzzy match
            return None, 0.0

    except Exception as e:
        print(f"⚠️ LLM matching failed: {e}")
        return None, 0.0


def listen_and_create():
    recognizer = sr.Recognizer()
    mic = sr.Microphone()
    print("🎤 Calibrating microphone...")
    with mic as source:
        recognizer.adjust_for_ambient_noise(source, duration=1)
    print("✅ Ready! Say your command in the format: 'Task name to List Name'.")

    # Preload lists
    lists = fetch_lists()
    print(f"🔍 Found lists: {', '.join(lists.keys())}")

    try:
        while True:
            with mic as source:
                print("Listening...")
                audio = recognizer.listen(source)

            try:
                text = recognizer.recognize_google(audio)
                print(f"📝 Heard: {text}")

                # Parse command
                if ' to ' in text.lower():
                    task_part, list_part = text.lower().split(' to ', 1)
                    list_name_spoken = list_part.strip()
                    task_name = task_part.strip().capitalize()

                    # First try exact match (for backward compatibility)
                    if list_name_spoken in lists:
                        list_id = lists[list_name_spoken]
                        result = create_clickup_task(list_id, task_name)
                        print(f"✅ Created task '{task_name}' in list '{list_name_spoken}'. ID: {result.get('id')}")
                    else:
                        # Use LLM to find the best match
                        print(f"🤖 Using AI to match '{list_name_spoken}' with available lists...")
                        matched_list, confidence = match_list_with_llm(list_name_spoken, lists)

                        if matched_list and confidence > 0.5:
                            list_id = lists[matched_list]
                            result = create_clickup_task(list_id, task_name)
                            print(f"✅ Created task '{task_name}' in list '{matched_list}' (AI matched with {confidence:.1f} confidence). ID: {result.get('id')}")
                        else:
                            print(f"⚠️ Could not find a good match for '{list_name_spoken}'. Available lists: {', '.join(lists.keys())}")
                else:
                    print("⚠️ Command format incorrect. Use 'Task name to List Name'.")

            except sr.UnknownValueError:
                print("⚠️ Could not understand audio.")
            except sr.RequestError as e:
                print(f"❌ Speech API error: {e}")
            except requests.HTTPError as e:
                print(f"❌ ClickUp API error: {e.response.text}")

            time.sleep(1)
    except KeyboardInterrupt:
        print("👋 Stopped. Bye!")


if __name__ == "__main__":
    listen_and_create()
