# Voice-to-Task with AI-Enhanced Speech Recognition

A voice-controlled task creation system for ClickUp with intelligent speech recognition powered by Google Gemini.

## Features

- **Voice Recognition**: Convert speech to text using Google Speech Recognition
- **AI-Enhanced Matching**: Use Google Gemini to intelligently match speech input with ClickUp lists
- **Fuzzy Matching**: Handle speech recognition errors and variations (e.g., "shopping list" → "shopping")
- **ClickUp Integration**: Automatically create tasks in the correct ClickUp lists
- **Fallback System**: Falls back to exact matching if AI matching fails

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Environment Variables

You need to set up three environment variables:

1. **ClickUp API Token**: Get this from your ClickUp settings
2. **ClickUp Team ID**: Your ClickUp workspace/team ID
3. **Gemini API Key**: Get this from Google AI Studio

#### Option A: Use the run.sh script (recommended)

Edit `run.sh` and replace the placeholder values:

```bash
export CLICKUP_TOKEN="your_clickup_token_here"
export CLICKUP_TEAM_ID="your_team_id_here"
export GEMINI_API_KEY="your_gemini_api_key_here"
```

#### Option B: Set environment variables manually

```bash
export CLICKUP_TOKEN="your_clickup_token_here"
export CLICKUP_TEAM_ID="your_team_id_here"
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 3. Get Your API Keys

#### ClickUp API Token:
1. Go to ClickUp Settings → Apps
2. Generate a new API token
3. Copy the token

#### ClickUp Team ID:
1. Go to your ClickUp workspace
2. The team ID is in the URL: `https://app.clickup.com/{TEAM_ID}/...`

#### Gemini API Key:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key

## Usage

### Running the Application

```bash
# Using the run script (recommended)
./run.sh

# Or run directly
python main.py
```

### Voice Commands

Speak in the format: **"Task name to List Name"**

Examples:
- "Buy groceries to shopping"
- "Call client to work"
- "Fix the sink to home tasks"

### AI-Enhanced Matching

The system now intelligently matches your speech with available lists:

- **"shopping list"** → matches **"shopping"**
- **"grocery list"** → matches **"groceries"**
- **"work stuff"** → matches **"work"**
- **"personal things"** → matches **"personal"**

## How It Works

1. **Speech Recognition**: Converts your voice to text using Google Speech Recognition
2. **Command Parsing**: Extracts task name and list name from "Task to List" format
3. **Smart Matching**: 
   - First tries exact matching for backward compatibility
   - If no exact match, uses Google Gemini to find the best matching list
   - Considers variations, synonyms, and speech recognition errors
4. **Task Creation**: Creates the task in ClickUp using the matched list

## Testing

Test the AI matching functionality without speech input:

```bash
python test_llm_matching.py
```

## Troubleshooting

### Common Issues

1. **"Could not understand audio"**: Speak clearly and ensure your microphone is working
2. **"List not found"**: Make sure the list exists in your ClickUp workspace
3. **"Speech API error"**: Check your internet connection
4. **"ClickUp API error"**: Verify your ClickUp token and team ID
5. **"LLM matching failed"**: Check your Gemini API key and internet connection

### Dependencies

- `speechrecognition`: For voice recognition
- `requests`: For ClickUp API calls
- `pyaudio`: For microphone access
- `google-generativeai`: For AI-enhanced list matching

## Files

- `main.py`: Main application with voice recognition and ClickUp integration
- `requirements.txt`: Python dependencies
- `run.sh`: Script to set environment variables and run the app
- `test_llm_matching.py`: Test script for AI matching functionality
- `README.md`: This documentation

## License

MIT License
