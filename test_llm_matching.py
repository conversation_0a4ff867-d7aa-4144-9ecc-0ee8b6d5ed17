#!/usr/bin/env python3
"""
Test script for the LLM list matching functionality.
This script tests the match_list_with_llm function without requiring actual speech input.
"""

import os
import sys

# Add the current directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_llm_matching():
    """Test the LLM matching function with various inputs."""
    
    # Mock environment variables for testing
    os.environ["CLICKUP_TOKEN"] = "********************************************"
    os.environ["CLICKUP_TEAM_ID"] = "90161110432"
    os.environ["GEMINI_API_KEY"] = "AIzaSyAvG5AgEOJwEQC7UTTTZuygMbtbx6QMGd4"  # You'll need to replace this with a real key
    
    try:
        from main import match_list_with_llm
        
        # Sample available lists (similar to what might be in ClickUp)
        test_lists = {
            "shopping": "list_id_1",
            "work": "list_id_2", 
            "personal": "list_id_3",
            "groceries": "list_id_4",
            "tasks": "list_id_5",
            "home": "list_id_6"
        }
        
        # Test cases: (speech_input, expected_match_or_none)
        test_cases = [
            ("shopping list", "shopping"),
            ("grocery list", "groceries"),
            ("work stuff", "work"),
            ("personal things", "personal"),
            ("home tasks", "home"),
            ("random nonsense", None),
            ("shop", "shopping"),
            ("groceries", "groceries")
        ]
        
        print("🧪 Testing LLM List Matching")
        print("=" * 50)
        print(f"Available lists: {list(test_lists.keys())}")
        print()
        
        for speech_input, expected in test_cases:
            print(f"Testing: '{speech_input}'")
            matched_list, confidence = match_list_with_llm(speech_input, test_lists)
            
            if matched_list:
                print(f"  ✅ Matched: '{matched_list}' (confidence: {confidence:.1f})")
                if expected and matched_list == expected:
                    print(f"  ✅ Expected match!")
                elif expected:
                    print(f"  ⚠️ Expected '{expected}' but got '{matched_list}'")
            else:
                print(f"  ❌ No match found")
                if expected is None:
                    print(f"  ✅ Expected no match!")
                else:
                    print(f"  ⚠️ Expected '{expected}' but got no match")
            print()
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure google-generativeai is installed: pip install google-generativeai")
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        print("Make sure you have set a valid GEMINI_API_KEY environment variable")

if __name__ == "__main__":
    test_llm_matching()
